<?php
/**
 * Test file for WooCommerce Email Ordini plugin
 * 
 * Access via: /wp-content/plugins/woo-email-ordini/test-plugin.php
 * 
 * IMPORTANT: Remove this file in production!
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    // Load WordPress
    require_once('../../../wp-load.php');
}

// Check if user is admin
if (!current_user_can('manage_options')) {
    wp_die('Access denied');
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>WooCommerce Email Ordini - Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        .debug-section { border: 1px solid #ddd; padding: 15px; margin: 10px 0; }
    </style>
</head>
<body>
    <h1>WooCommerce Email Ordini - Plugin Test</h1>
    
    <div class="debug-section">
        <h2>Plugin Status</h2>
        <?php
        // Check if our plugin constants are defined
        if (defined('WOO_EMAIL_ORDINI_VERSION')) {
            echo '<p class="success">✓ Plugin loaded successfully</p>';
            echo '<p>Version: ' . WOO_EMAIL_ORDINI_VERSION . '</p>';
        } else {
            echo '<p class="error">✗ Plugin not loaded</p>';
        }
        
        // Check if WooCommerce is active
        if (class_exists('WooCommerce')) {
            echo '<p class="success">✓ WooCommerce is active</p>';
        } else {
            echo '<p class="error">✗ WooCommerce is not active</p>';
        }
        
        // Check if our classes exist
        if (class_exists('Woo_Email_Ordini_Admin')) {
            echo '<p class="success">✓ Admin class loaded</p>';
        } else {
            echo '<p class="error">✗ Admin class not loaded</p>';
        }
        
        if (class_exists('Woo_Email_Ordini_CSV_Exporter')) {
            echo '<p class="success">✓ CSV Exporter class loaded</p>';
        } else {
            echo '<p class="error">✗ CSV Exporter class not loaded</p>';
        }
        ?>
    </div>
    
    <div class="debug-section">
        <h2>WooCommerce Orders Test</h2>
        <?php
        if (class_exists('WooCommerce')) {
            // Get some sample orders
            $orders = wc_get_orders(array(
                'limit' => 5,
                'status' => array('wc-completed', 'wc-processing', 'wc-pending'),
                'return' => 'objects'
            ));
            
            echo '<p>Found ' . count($orders) . ' recent orders</p>';
            
            if (!empty($orders)) {
                echo '<ul>';
                foreach ($orders as $order) {
                    echo '<li>Order #' . $order->get_order_number() . ' - ' . $order->get_status() . ' - ' . $order->get_billing_email() . ' - ' . $order->get_date_created()->date('Y-m-d H:i:s') . '</li>';
                }
                echo '</ul>';
            }
            
            // Test order statuses
            $statuses = wc_get_order_statuses();
            echo '<h3>Available Order Statuses:</h3>';
            echo '<ul>';
            foreach ($statuses as $status_key => $status_label) {
                echo '<li>' . $status_key . ' - ' . $status_label . '</li>';
            }
            echo '</ul>';
        }
        ?>
    </div>
    
    <div class="debug-section">
        <h2>Debug Settings</h2>
        <?php
        if (defined('WP_DEBUG') && WP_DEBUG) {
            echo '<p class="success">✓ WP_DEBUG is enabled</p>';
        } else {
            echo '<p class="error">✗ WP_DEBUG is disabled</p>';
        }
        
        if (defined('WP_DEBUG_LOG') && WP_DEBUG_LOG) {
            echo '<p class="success">✓ WP_DEBUG_LOG is enabled</p>';
        } else {
            echo '<p class="error">✗ WP_DEBUG_LOG is disabled</p>';
        }
        ?>
    </div>
    
    <div class="debug-section">
        <h2>Test CSV Export Function</h2>
        <?php
        if (class_exists('Woo_Email_Ordini_CSV_Exporter')) {
            $exporter = new Woo_Email_Ordini_CSV_Exporter();
            
            // Test with recent date range
            $start_date = date('Y-m-d', strtotime('-30 days'));
            $end_date = date('Y-m-d');
            $statuses = array('wc-completed', 'wc-processing');
            
            echo '<p>Testing export with:</p>';
            echo '<ul>';
            echo '<li>Start Date: ' . $start_date . '</li>';
            echo '<li>End Date: ' . $end_date . '</li>';
            echo '<li>Statuses: ' . implode(', ', $statuses) . '</li>';
            echo '</ul>';
            
            $order_count = $exporter->get_order_count($start_date, $end_date, $statuses);
            $email_count = $exporter->get_email_count($start_date, $end_date, $statuses);
            
            echo '<p>Orders found: ' . $order_count . '</p>';
            echo '<p>Unique emails: ' . $email_count . '</p>';
        }
        ?>
    </div>
    
    <p><a href="<?php echo admin_url('admin.php?page=woo-email-ordini'); ?>">Go to Email Ordini Page</a></p>
    <p><small><strong>Note:</strong> This test file should be removed in production environments for security reasons.</small></p>
</body>
</html>
