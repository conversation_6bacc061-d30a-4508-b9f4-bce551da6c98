/**
 * Admin JavaScript for WooCommerce Email Ordini plugin
 */

jQuery(document).ready(function($) {
    'use strict';

    var EmailOrdiniAdmin = {
        
        /**
         * Initialize the admin functionality
         */
        init: function() {
            this.bindEvents();
            this.setDefaultDates();
        },

        /**
         * Bind events
         */
        bindEvents: function() {
            // Form validation on submit
            $('.email-ordini-form').on('submit', this.validateForm);
            
            // Date validation on change
            $('#start_date, #end_date').on('change', this.validateDates);
            
            // Status checkbox validation
            $('input[name="order_statuses[]"]').on('change', this.validateStatuses);
            
            // Select all / Deselect all functionality
            this.addSelectAllButtons();
        },

        /**
         * Set default dates (last 30 days)
         */
        setDefaultDates: function() {
            var today = new Date();
            var thirtyDaysAgo = new Date();
            thirtyDaysAgo.setDate(today.getDate() - 30);
            
            if (!$('#start_date').val()) {
                $('#start_date').val(this.formatDate(thirtyDaysAgo));
            }
            
            if (!$('#end_date').val()) {
                $('#end_date').val(this.formatDate(today));
            }
        },

        /**
         * Format date to YYYY-MM-DD
         */
        formatDate: function(date) {
            var year = date.getFullYear();
            var month = ('0' + (date.getMonth() + 1)).slice(-2);
            var day = ('0' + date.getDate()).slice(-2);
            return year + '-' + month + '-' + day;
        },

        /**
         * Validate form before submission
         */
        validateForm: function(e) {
            var isValid = true;
            var errorMessages = [];

            // Clear previous errors
            $('.email-ordini-error').remove();

            // Validate dates
            var startDate = $('#start_date').val();
            var endDate = $('#end_date').val();

            if (!startDate || !endDate) {
                errorMessages.push('Le date sono obbligatorie.');
                isValid = false;
            } else if (new Date(startDate) > new Date(endDate)) {
                errorMessages.push(wooEmailOrdini.strings.date_error);
                isValid = false;
            }

            // Validate order statuses
            var checkedStatuses = $('input[name="order_statuses[]"]:checked').length;
            if (checkedStatuses === 0) {
                errorMessages.push(wooEmailOrdini.strings.status_error);
                isValid = false;
            }

            // Show errors if any
            if (!isValid) {
                e.preventDefault();
                EmailOrdiniAdmin.showErrors(errorMessages);
                return false;
            }

            // Show loading state
            EmailOrdiniAdmin.showLoading();
            return true;
        },

        /**
         * Validate dates on change
         */
        validateDates: function() {
            var startDate = $('#start_date').val();
            var endDate = $('#end_date').val();
            
            $('.date-error').remove();

            if (startDate && endDate && new Date(startDate) > new Date(endDate)) {
                $('#end_date').after('<div class="email-ordini-error date-error">' + wooEmailOrdini.strings.date_error + '</div>');
            }
        },

        /**
         * Validate status checkboxes
         */
        validateStatuses: function() {
            var checkedStatuses = $('input[name="order_statuses[]"]:checked').length;
            
            $('.status-error').remove();

            if (checkedStatuses === 0) {
                $('input[name="order_statuses[]"]').first().closest('fieldset').after('<div class="email-ordini-error status-error">' + wooEmailOrdini.strings.status_error + '</div>');
            }
        },

        /**
         * Add select all / deselect all buttons
         */
        addSelectAllButtons: function() {
            var fieldset = $('input[name="order_statuses[]"]').first().closest('fieldset');
            var buttonsHtml = '<div style="margin-bottom: 10px;">' +
                '<button type="button" class="button button-small select-all-statuses">Seleziona Tutti</button> ' +
                '<button type="button" class="button button-small deselect-all-statuses">Deseleziona Tutti</button>' +
                '</div>';
            
            fieldset.prepend(buttonsHtml);

            // Bind events for the new buttons
            $('.select-all-statuses').on('click', function(e) {
                e.preventDefault();
                $('input[name="order_statuses[]"]').prop('checked', true);
                EmailOrdiniAdmin.validateStatuses();
            });

            $('.deselect-all-statuses').on('click', function(e) {
                e.preventDefault();
                $('input[name="order_statuses[]"]').prop('checked', false);
                EmailOrdiniAdmin.validateStatuses();
            });
        },

        /**
         * Show error messages
         */
        showErrors: function(messages) {
            var errorHtml = '<div class="notice notice-error"><ul>';
            messages.forEach(function(message) {
                errorHtml += '<li>' + message + '</li>';
            });
            errorHtml += '</ul></div>';
            
            $('.wrap h1').after(errorHtml);
            
            // Scroll to top to show errors
            $('html, body').animate({
                scrollTop: $('.wrap').offset().top
            }, 500);
        },

        /**
         * Show loading state
         */
        showLoading: function() {
            var submitButton = $('input[name="export_csv"]');
            submitButton.prop('disabled', true);
            submitButton.val(wooEmailOrdini.strings.exporting);
            $('.email-ordini-form').addClass('loading');
        }
    };

    // Initialize when document is ready
    EmailOrdiniAdmin.init();
});
