<?php
/**
 * CSV Exporter for WooCommerce Email Ordini plugin
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class Woo_Email_Ordini_CSV_Exporter {

    /**
     * Export orders to CSV
     */
    public function export($start_date, $end_date, $order_statuses) {
        // Debug logging
        $this->debug_log('Starting export with parameters', array(
            'start_date' => $start_date,
            'end_date' => $end_date,
            'order_statuses' => $order_statuses
        ));

        // Get orders based on criteria
        $orders = $this->get_orders($start_date, $end_date, $order_statuses);

        $this->debug_log('Orders found', array('count' => count($orders)));

        if (empty($orders)) {
            $this->debug_log('No orders found - returning error');
            wp_die(__('Nessun ordine trovato con i criteri specificati.', 'woo-email-ordini'));
            return;
        }

        // Extract unique emails
        $emails = $this->extract_emails($orders);

        $this->debug_log('Emails extracted', array('count' => count($emails)));

        if (empty($emails)) {
            $this->debug_log('No emails found - returning error');
            wp_die(__('Nessuna email trovata negli ordini selezionati.', 'woo-email-ordini'));
            return;
        }

        // Generate CSV
        $this->debug_log('Generating CSV');
        $this->generate_csv($emails, $start_date, $end_date, $order_statuses);
    }

    /**
     * Get orders based on date range and statuses
     */
    private function get_orders($start_date, $end_date, $order_statuses) {
        // Convert date format for WooCommerce query
        $start_timestamp = strtotime($start_date . ' 00:00:00');
        $end_timestamp = strtotime($end_date . ' 23:59:59');

        $this->debug_log('Date conversion', array(
            'start_date' => $start_date,
            'end_date' => $end_date,
            'start_timestamp' => $start_timestamp,
            'end_timestamp' => $end_timestamp
        ));

        $args = array(
            'limit' => -1,
            'status' => $order_statuses,
            'date_created' => $start_timestamp . '...' . $end_timestamp,
            'return' => 'objects'
        );

        $this->debug_log('WooCommerce query args', $args);

        $orders = wc_get_orders($args);

        $this->debug_log('Query result', array(
            'orders_count' => count($orders),
            'first_order_id' => !empty($orders) ? $orders[0]->get_id() : 'none'
        ));

        return $orders;
    }

    /**
     * Extract unique emails from orders
     */
    private function extract_emails($orders) {
        $emails = array();
        
        foreach ($orders as $order) {
            $billing_email = $order->get_billing_email();
            if (!empty($billing_email) && is_email($billing_email)) {
                $emails[] = $billing_email;
            }
        }
        
        // Remove duplicates and return
        return array_unique($emails);
    }

    /**
     * Generate and download CSV file
     */
    private function generate_csv($emails, $start_date, $end_date, $order_statuses) {
        // Set headers for CSV download
        $filename = 'email-ordini-' . $start_date . '-' . $end_date . '-' . date('Y-m-d-H-i-s') . '.csv';
        
        header('Content-Type: text/csv; charset=utf-8');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Pragma: no-cache');
        header('Expires: 0');

        // Create file pointer connected to the output stream
        $output = fopen('php://output', 'w');

        // Add BOM for UTF-8 (helps with Excel compatibility)
        fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));

        // Add CSV header
        fputcsv($output, array('Email'), ';');

        // Add email data
        foreach ($emails as $email) {
            fputcsv($output, array($email), ';');
        }

        // Add summary information as comments at the end
        fputcsv($output, array(''), ';');
        fputcsv($output, array('# Riepilogo Esportazione'), ';');
        fputcsv($output, array('# Data Iniziale: ' . $start_date), ';');
        fputcsv($output, array('# Data Finale: ' . $end_date), ';');
        fputcsv($output, array('# Status Ordini: ' . implode(', ', $order_statuses)), ';');
        fputcsv($output, array('# Totale Email: ' . count($emails)), ';');
        fputcsv($output, array('# Data Esportazione: ' . date('Y-m-d H:i:s')), ';');

        fclose($output);
        exit;
    }

    /**
     * Get order count for preview (optional method for future use)
     */
    public function get_order_count($start_date, $end_date, $order_statuses) {
        $args = array(
            'limit' => -1,
            'status' => $order_statuses,
            'date_created' => $start_date . '...' . $end_date,
            'return' => 'ids'
        );

        $order_ids = wc_get_orders($args);
        return count($order_ids);
    }

    /**
     * Get unique email count for preview (optional method for future use)
     */
    public function get_email_count($start_date, $end_date, $order_statuses) {
        $orders = $this->get_orders($start_date, $end_date, $order_statuses);
        $emails = $this->extract_emails($orders);
        return count($emails);
    }

    /**
     * Debug logging helper
     */
    private function debug_log($message, $data = null) {
        if (defined('WP_DEBUG') && WP_DEBUG) {
            $log_message = 'WOO_EMAIL_ORDINI: ' . $message;
            if ($data !== null) {
                $log_message .= ' - ' . wp_json_encode($data);
            }
            error_log($log_message);
        }
    }
}
