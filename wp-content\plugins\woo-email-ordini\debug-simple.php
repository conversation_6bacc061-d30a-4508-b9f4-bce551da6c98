<?php
/**
 * Simple debug file for WooCommerce Email Ordini plugin
 */

// Load WordPress
require_once(__DIR__ . '/../../../wp-load.php');

echo "WooCommerce Email Ordini Debug\n";
echo "==============================\n\n";

// Check if plugin constants are defined
if (defined('WOO_EMAIL_ORDINI_VERSION')) {
    echo "✓ Plugin loaded successfully\n";
    echo "Version: " . WOO_EMAIL_ORDINI_VERSION . "\n";
} else {
    echo "✗ Plugin not loaded\n";
}

// Check if WooCommerce is active
if (class_exists('WooCommerce')) {
    echo "✓ WooCommerce is active\n";
} else {
    echo "✗ WooCommerce is not active\n";
}

// Check if our classes exist
if (class_exists('Woo_Email_Ordini_Admin')) {
    echo "✓ Admin class loaded\n";
} else {
    echo "✗ Admin class not loaded\n";
}

if (class_exists('Woo_Email_Ordini_CSV_Exporter')) {
    echo "✓ CSV Exporter class loaded\n";
} else {
    echo "✗ CSV Exporter class not loaded\n";
}

// Test WooCommerce orders
if (class_exists('WooCommerce')) {
    $orders = wc_get_orders(array(
        'limit' => 5,
        'status' => array('wc-completed', 'wc-processing', 'wc-pending'),
        'return' => 'objects'
    ));
    
    echo "\nFound " . count($orders) . " recent orders\n";
    
    if (!empty($orders)) {
        foreach ($orders as $order) {
            echo "- Order #" . $order->get_order_number() . " - " . $order->get_status() . " - " . $order->get_billing_email() . "\n";
        }
    }
}

// Test CSV exporter if available
if (class_exists('Woo_Email_Ordini_CSV_Exporter')) {
    echo "\nTesting CSV Exporter...\n";
    $exporter = new Woo_Email_Ordini_CSV_Exporter();
    
    $start_date = date('Y-m-d', strtotime('-30 days'));
    $end_date = date('Y-m-d');
    $statuses = array('wc-completed', 'wc-processing');
    
    echo "Date range: $start_date to $end_date\n";
    echo "Statuses: " . implode(', ', $statuses) . "\n";
    
    try {
        $order_count = $exporter->get_order_count($start_date, $end_date, $statuses);
        $email_count = $exporter->get_email_count($start_date, $end_date, $statuses);

        echo "Orders found: $order_count\n";
        echo "Unique emails: $email_count\n";

        // Test the actual export method (but don't download)
        echo "\nTesting export method...\n";
        ob_start();
        try {
            $exporter->export($start_date, $end_date, $statuses);
            echo "Export method executed successfully\n";
        } catch (Exception $e) {
            echo "Export method error: " . $e->getMessage() . "\n";
        }
        ob_end_clean();

    } catch (Exception $e) {
        echo "Error: " . $e->getMessage() . "\n";
    }
}

echo "\nDebug completed.\n";
