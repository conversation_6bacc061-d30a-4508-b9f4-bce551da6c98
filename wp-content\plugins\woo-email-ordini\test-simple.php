<?php
/**
 * Plugin Name: Test Simple Email Ordini
 * Description: Test semplice per verificare che il plugin funzioni
 * Version: 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Add admin menu
add_action('admin_menu', 'test_simple_menu');

function test_simple_menu() {
    add_submenu_page(
        'woocommerce',
        'Test Simple',
        'Test Simple',
        'manage_options',
        'test-simple',
        'test_simple_page'
    );
}

function test_simple_page() {
    // Handle form submission
    if (isset($_POST['test_submit'])) {
        error_log('TEST_SIMPLE: Form submitted!');
        
        $start_date = sanitize_text_field($_POST['start_date']);
        $end_date = sanitize_text_field($_POST['end_date']);
        $statuses = isset($_POST['order_statuses']) ? $_POST['order_statuses'] : array();
        
        error_log('TEST_SIMPLE: Start date: ' . $start_date);
        error_log('TEST_SIMPLE: End date: ' . $end_date);
        error_log('TEST_SIMPLE: Statuses: ' . print_r($statuses, true));
        
        // Get orders
        $orders = wc_get_orders(array(
            'limit' => -1,
            'status' => $statuses,
            'date_created' => $start_date . '...' . $end_date,
            'return' => 'objects'
        ));
        
        error_log('TEST_SIMPLE: Orders found: ' . count($orders));
        
        // Extract emails
        $emails = array();
        foreach ($orders as $order) {
            $email = $order->get_billing_email();
            if (!empty($email)) {
                $emails[] = $email;
            }
        }
        $emails = array_unique($emails);
        
        error_log('TEST_SIMPLE: Unique emails: ' . count($emails));
        
        if (!empty($emails)) {
            // Generate CSV
            $filename = 'test-emails-' . date('Y-m-d-H-i-s') . '.csv';
            
            header('Content-Type: text/csv; charset=utf-8');
            header('Content-Disposition: attachment; filename="' . $filename . '"');
            header('Pragma: no-cache');
            header('Expires: 0');

            $output = fopen('php://output', 'w');
            fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));
            fputcsv($output, array('Email'), ';');

            foreach ($emails as $email) {
                fputcsv($output, array($email), ';');
            }

            fclose($output);
            exit;
        } else {
            echo '<div class="notice notice-warning"><p>Nessuna email trovata!</p></div>';
        }
    }
    
    // Get order statuses
    $statuses = wc_get_order_statuses();
    
    ?>
    <div class="wrap">
        <h1>Test Simple Email Export</h1>
        
        <form method="post">
            <table class="form-table">
                <tr>
                    <th><label for="start_date">Data Iniziale</label></th>
                    <td><input type="date" id="start_date" name="start_date" required /></td>
                </tr>
                <tr>
                    <th><label for="end_date">Data Finale</label></th>
                    <td><input type="date" id="end_date" name="end_date" required /></td>
                </tr>
                <tr>
                    <th>Status Ordine</th>
                    <td>
                        <?php foreach ($statuses as $status_key => $status_label) : ?>
                            <label>
                                <input type="checkbox" name="order_statuses[]" value="<?php echo esc_attr($status_key); ?>" />
                                <?php echo esc_html($status_label); ?>
                            </label><br>
                        <?php endforeach; ?>
                    </td>
                </tr>
            </table>
            
            <p class="submit">
                <input type="submit" name="test_submit" class="button button-primary" value="Test Export" />
            </p>
        </form>
    </div>
    <?php
}
