<?php
/**
 * Plugin Name: WooCommerce Email Ordini
 * Plugin URI: https://example.com/woo-email-ordini
 * Description: Plugin per esportare le email degli ordini WooCommerce in base a status e range di date.
 * Version: 1.0.0
 * Author: <PERSON> '<PERSON><PERSON><PERSON>' <PERSON>
 * Author URI: https://github.com/JoJoD3v
 * Text Domain: woo-email-ordini
 * Domain Path: /languages
 * Requires at least: 5.0
 * Tested up to: 6.4
 * Requires PHP: 7.4
 * WC requires at least: 5.0
 * WC tested up to: 8.0
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('WOO_EMAIL_ORDINI_VERSION', '1.0.0');
define('WOO_EMAIL_ORDINI_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('WOO_EMAIL_ORDINI_PLUGIN_URL', plugin_dir_url(__FILE__));
define('WOO_EMAIL_ORDINI_PLUGIN_BASENAME', plugin_basename(__FILE__));

/**
 * Main plugin class
 */
class Woo_Email_Ordini {

    /**
     * Single instance of the class
     */
    private static $instance = null;

    /**
     * Get single instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Constructor
     */
    private function __construct() {
        add_action('plugins_loaded', array($this, 'init'));
        register_activation_hook(__FILE__, array($this, 'activate'));
        register_deactivation_hook(__FILE__, array($this, 'deactivate'));
    }

    /**
     * Initialize the plugin
     */
    public function init() {
        // Check if WooCommerce is active
        if (!class_exists('WooCommerce')) {
            add_action('admin_notices', array($this, 'woocommerce_missing_notice'));
            return;
        }

        // Load text domain
        add_action('init', array($this, 'load_textdomain'));

        // Include required files
        $this->includes();

        // Initialize admin functionality
        if (is_admin()) {
            new Woo_Email_Ordini_Admin();
        }
    }

    /**
     * Load text domain for translations
     */
    public function load_textdomain() {
        load_plugin_textdomain('woo-email-ordini', false, dirname(WOO_EMAIL_ORDINI_PLUGIN_BASENAME) . '/languages');
    }

    /**
     * Include required files
     */
    private function includes() {
        require_once WOO_EMAIL_ORDINI_PLUGIN_DIR . 'includes/class-admin.php';
        require_once WOO_EMAIL_ORDINI_PLUGIN_DIR . 'includes/class-csv-exporter.php';
    }

    /**
     * Plugin activation
     */
    public function activate() {
        // Check WooCommerce dependency
        if (!class_exists('WooCommerce')) {
            deactivate_plugins(WOO_EMAIL_ORDINI_PLUGIN_BASENAME);
            wp_die(
                __('WooCommerce Email Ordini requires WooCommerce to be installed and active.', 'woo-email-ordini'),
                __('Plugin Activation Error', 'woo-email-ordini'),
                array('back_link' => true)
            );
        }
    }

    /**
     * Plugin deactivation
     */
    public function deactivate() {
        // Clean up if necessary
    }

    /**
     * WooCommerce missing notice
     */
    public function woocommerce_missing_notice() {
        ?>
        <div class="notice notice-error">
            <p><?php _e('WooCommerce Email Ordini requires WooCommerce to be installed and active.', 'woo-email-ordini'); ?></p>
        </div>
        <?php
    }
}

// Initialize the plugin
Woo_Email_Ordini::get_instance();
