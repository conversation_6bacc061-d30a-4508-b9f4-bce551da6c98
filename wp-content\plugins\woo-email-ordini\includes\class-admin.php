<?php
/**
 * Admin functionality for WooCommerce Email Ordini plugin
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class Woo_Email_Ordini_Admin {

    /**
     * Constructor
     */
    public function __construct() {
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));
        add_action('admin_init', array($this, 'handle_csv_export'));
    }

    /**
     * Add admin menu item under WooCommerce
     */
    public function add_admin_menu() {
        add_submenu_page(
            'woocommerce',
            __('Email Ordini', 'woo-email-ordini'),
            __('Email Ordini', 'woo-email-ordini'),
            'manage_woocommerce',
            'woo-email-ordini',
            array($this, 'admin_page')
        );
    }

    /**
     * Enqueue admin scripts and styles
     */
    public function enqueue_admin_scripts($hook) {
        if ('woocommerce_page_woo-email-ordini' !== $hook) {
            return;
        }

        wp_enqueue_style(
            'woo-email-ordini-admin',
            WOO_EMAIL_ORDINI_PLUGIN_URL . 'assets/css/admin.css',
            array(),
            WOO_EMAIL_ORDINI_VERSION
        );

        wp_enqueue_script(
            'woo-email-ordini-admin',
            WOO_EMAIL_ORDINI_PLUGIN_URL . 'assets/js/admin.js',
            array('jquery'),
            WOO_EMAIL_ORDINI_VERSION,
            true
        );

        wp_localize_script('woo-email-ordini-admin', 'wooEmailOrdini', array(
            'strings' => array(
                'date_error' => __('La data iniziale non può essere successiva alla data finale.', 'woo-email-ordini'),
                'status_error' => __('Seleziona almeno uno status ordine.', 'woo-email-ordini'),
                'exporting' => __('Esportazione in corso...', 'woo-email-ordini'),
            )
        ));
    }

    /**
     * Get all available order statuses
     */
    private function get_order_statuses() {
        $statuses = wc_get_order_statuses();
        return $statuses;
    }

    /**
     * Handle CSV export
     */
    public function handle_csv_export() {
        if (!isset($_POST['export_csv']) || !wp_verify_nonce($_POST['_wpnonce'], 'woo_email_ordini_export')) {
            return;
        }

        if (!current_user_can('manage_woocommerce')) {
            wp_die(__('Non hai i permessi sufficienti per accedere a questa pagina.', 'woo-email-ordini'));
        }

        $start_date = sanitize_text_field($_POST['start_date']);
        $end_date = sanitize_text_field($_POST['end_date']);
        $order_statuses = isset($_POST['order_statuses']) ? array_map('sanitize_text_field', $_POST['order_statuses']) : array();

        // Validation
        if (empty($start_date) || empty($end_date)) {
            add_action('admin_notices', function() {
                echo '<div class="notice notice-error"><p>' . __('Le date sono obbligatorie.', 'woo-email-ordini') . '</p></div>';
            });
            return;
        }

        if (empty($order_statuses)) {
            add_action('admin_notices', function() {
                echo '<div class="notice notice-error"><p>' . __('Seleziona almeno uno status ordine.', 'woo-email-ordini') . '</p></div>';
            });
            return;
        }

        if (strtotime($start_date) > strtotime($end_date)) {
            add_action('admin_notices', function() {
                echo '<div class="notice notice-error"><p>' . __('La data iniziale non può essere successiva alla data finale.', 'woo-email-ordini') . '</p></div>';
            });
            return;
        }

        // Export CSV
        $csv_exporter = new Woo_Email_Ordini_CSV_Exporter();
        $csv_exporter->export($start_date, $end_date, $order_statuses);
    }

    /**
     * Admin page content
     */
    public function admin_page() {
        $order_statuses = $this->get_order_statuses();
        ?>
        <div class="wrap">
            <h1><?php _e('Esporta Email Ordini', 'woo-email-ordini'); ?></h1>
            <p><?php _e('Utilizza questo strumento per esportare le email degli ordini in base al range di date e status selezionati.', 'woo-email-ordini'); ?></p>
            
            <form method="post" class="email-ordini-form">
                <?php wp_nonce_field('woo_email_ordini_export'); ?>
                
                <table class="form-table">
                    <tr>
                        <th scope="row">
                            <label for="start_date"><?php _e('Data Iniziale', 'woo-email-ordini'); ?> <span class="required">*</span></label>
                        </th>
                        <td>
                            <input type="date" id="start_date" name="start_date" required />
                            <p class="description"><?php _e('Seleziona la data di inizio del range.', 'woo-email-ordini'); ?></p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">
                            <label for="end_date"><?php _e('Data Finale', 'woo-email-ordini'); ?> <span class="required">*</span></label>
                        </th>
                        <td>
                            <input type="date" id="end_date" name="end_date" required />
                            <p class="description"><?php _e('Seleziona la data di fine del range.', 'woo-email-ordini'); ?></p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">
                            <label><?php _e('Status Ordine', 'woo-email-ordini'); ?> <span class="required">*</span></label>
                        </th>
                        <td>
                            <fieldset>
                                <legend class="screen-reader-text"><?php _e('Seleziona gli status degli ordini', 'woo-email-ordini'); ?></legend>
                                <?php foreach ($order_statuses as $status_key => $status_label) : ?>
                                    <label>
                                        <input type="checkbox" name="order_statuses[]" value="<?php echo esc_attr($status_key); ?>" />
                                        <?php echo esc_html($status_label); ?>
                                    </label><br>
                                <?php endforeach; ?>
                                <p class="description"><?php _e('Seleziona uno o più status degli ordini da includere nell\'esportazione.', 'woo-email-ordini'); ?></p>
                            </fieldset>
                        </td>
                    </tr>
                </table>
                
                <p class="submit">
                    <input type="submit" name="export_csv" class="button button-primary" value="<?php _e('Scarica CSV', 'woo-email-ordini'); ?>" />
                </p>
            </form>
        </div>
        <?php
    }
}
