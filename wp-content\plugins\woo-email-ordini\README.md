# WooCommerce Email Ordini

Plugin WordPress per esportare le email degli ordini WooCommerce in formato CSV basato su range di date e status degli ordini.

## Descrizione

Questo plugin aggiunge un submenu "Email Ordini" nella sezione WooCommerce dell'admin di WordPress, permettendo agli amministratori di:

- Selezionare un range di date (data iniziale e finale)
- Scegliere uno o più status degli ordini
- Esportare tutte le email uniche degli ordini che corrispondono ai criteri in un file CSV

## Caratteristiche

- **Interfaccia semplice**: Form intuitivo con validazione JavaScript
- **Filtri flessibili**: Selezione multipla degli status ordini
- **Export CSV**: Download diretto del file con email uniche
- **Validazione**: Controlli lato client e server
- **Responsive**: Interfaccia adattiva per dispositivi mobili
- **Sicurezza**: Nonce verification e controlli permessi

## Installazione

1. Carica la cartella `woo-email-ordini` nella directory `/wp-content/plugins/`
2. Attiva il plugin attraverso il menu 'Plugins' in WordPress
3. Assicurati che WooCommerce sia installato e attivo

## Utilizzo

1. Vai su **WooCommerce > Email Ordini** nell'admin di WordPress
2. Seleziona la **Data Iniziale** e **Data Finale**
3. Scegli uno o più **Status Ordine** dalle checkbox disponibili
4. Clicca su **Scarica CSV** per esportare le email

## Struttura File CSV

Il file CSV generato contiene:
- Una colonna "Email" con tutte le email uniche
- Informazioni di riepilogo alla fine del file (come commenti)
- Encoding UTF-8 con BOM per compatibilità Excel

## Requisiti

- WordPress 5.0 o superiore
- WooCommerce 5.0 o superiore
- PHP 7.4 o superiore

## Struttura Plugin

```
woo-email-ordini/
├── woo-email-ordini.php          # File principale del plugin
├── includes/
│   ├── class-admin.php           # Gestione interfaccia admin
│   └── class-csv-exporter.php    # Logica esportazione CSV
├── assets/
│   ├── css/
│   │   └── admin.css             # Stili interfaccia admin
│   └── js/
│       └── admin.js              # JavaScript validazione form
└── README.md                     # Documentazione
```

## Funzionalità Tecniche

### Classe Principale (`Woo_Email_Ordini`)
- Singleton pattern
- Controllo dipendenze WooCommerce
- Caricamento classi e inizializzazione

### Classe Admin (`Woo_Email_Ordini_Admin`)
- Aggiunta submenu WooCommerce
- Gestione form e validazione
- Enqueue scripts e stili

### Classe CSV Exporter (`Woo_Email_Ordini_CSV_Exporter`)
- Query ordini con filtri
- Estrazione email uniche
- Generazione e download CSV

## Sicurezza

- Controllo permessi `manage_woocommerce`
- Nonce verification per form submission
- Sanitizzazione input utente
- Validazione date e status

## Personalizzazioni

Il plugin può essere facilmente esteso per:
- Aggiungere campi aggiuntivi al CSV
- Modificare i filtri disponibili
- Personalizzare il formato di export
- Aggiungere notifiche email

## Supporto

Per supporto e segnalazione bug, contatta lo sviluppatore.

## Licenza

GPL v2 or later

## Changelog

### 1.0.0
- Prima release
- Funzionalità base di esportazione CSV
- Interfaccia admin completa
- Validazione form JavaScript
