/**
 * Admin styles for WooCommerce Email Ordini plugin
 */

.email-ordini-form {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    padding: 20px;
    margin-top: 20px;
}

.email-ordini-form .form-table th {
    width: 200px;
    padding: 15px 10px 15px 0;
}

.email-ordini-form .form-table td {
    padding: 15px 10px;
}

.email-ordini-form input[type="date"] {
    width: 200px;
}

.email-ordini-form .required {
    color: #d63638;
}

.email-ordini-form fieldset {
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 15px;
    background: #f9f9f9;
}

.email-ordini-form fieldset label {
    display: block;
    margin-bottom: 8px;
    font-weight: normal;
}

.email-ordini-form fieldset label input[type="checkbox"] {
    margin-right: 8px;
}

.email-ordini-form .description {
    color: #666;
    font-style: italic;
    margin-top: 5px;
}

.email-ordini-form .submit {
    border-top: 1px solid #ddd;
    padding-top: 20px;
    margin-top: 20px;
}

.email-ordini-form .button-primary {
    background: #2271b1;
    border-color: #2271b1;
    color: #fff;
    text-decoration: none;
    text-shadow: none;
    font-size: 14px;
    line-height: 2.15384615;
    min-height: 32px;
    padding: 0 12px;
    border-radius: 3px;
}

.email-ordini-form .button-primary:hover {
    background: #135e96;
    border-color: #135e96;
}

.email-ordini-form .button-primary:disabled {
    background: #a7aaad !important;
    border-color: #a7aaad !important;
    color: #fff !important;
    cursor: not-allowed;
}

/* Status checkboxes styling */
.status-checkboxes {
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid #ddd;
    padding: 10px;
    background: #fff;
}

.status-checkboxes label {
    display: block;
    padding: 5px 0;
    border-bottom: 1px solid #f0f0f0;
}

.status-checkboxes label:last-child {
    border-bottom: none;
}

/* Loading state */
.email-ordini-form.loading {
    opacity: 0.6;
    pointer-events: none;
}

.email-ordini-form.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #2271b1;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Error messages */
.email-ordini-error {
    color: #d63638;
    font-weight: bold;
    margin-top: 5px;
}

/* Success messages */
.email-ordini-success {
    color: #00a32a;
    font-weight: bold;
    margin-top: 5px;
}

/* Responsive design */
@media screen and (max-width: 782px) {
    .email-ordini-form .form-table th,
    .email-ordini-form .form-table td {
        display: block;
        width: 100%;
        padding: 10px 0;
    }
    
    .email-ordini-form .form-table th {
        border-bottom: none;
    }
    
    .email-ordini-form input[type="date"] {
        width: 100%;
        max-width: 300px;
    }
}
